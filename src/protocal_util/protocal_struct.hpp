/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 协议结构体
 *        1 定义所有协议结构体
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include <string>
#include <vector>
#include "protcal_datatype.h"
#include <unordered_map>
namespace cvis_bcl
{

#pragma pack(1)

    struct WayPointLLEV
    {
        double longitude=-180;
        double latitude=-90;
        double elevation=-10000;
        float velocity=0;
        int GetLength()
        {
            return 14;
        }
    };
       struct WayPointLLE
    {
        double longitude=-180;
        double latitude=-90;
        double elevation=-10000;
        int GetLength()
        {
            return 12;
        }
    };


    struct WayPointLL
    {
        double longitude=-180;
        double latitude=-90;
        int GetLength()
        {
            return 8;
        }
    };

    struct Size
    {
        DWORD length=0;
        DWORD width=0;
        DWORD height=0;
        int GetLength()
        {
            return 12;
        }
    };

    struct SpeedLimit
    {   
        BYTE lane_id=0;
        BYTE spped_limit=0;
        int GetLength()
        {
            return 2;
        }
    };

    struct TrajectoryPoint
    {
        float x=-100.0;
        float y=-300.0;
        float z=-100.0;
        float theta=-180.0;
        float kappa=0;
        float s=-100.0;
        float dkappa=-100.0;
        float v=0;
        float a=-100.0;
        DWORD relative_time=-3600000;
        int GetLength()
        {
            return 24;
        }
    };


    struct HeartBeatData
    {
        DWORD msg_seq;
        TIMESTAMP timestamp;
        BYTE veh_id[8];
    };

    struct INHData
    {
        //DWORD msg_seq;
        BYTE veh_id[8];
        //[0..255]，描述车载终端设备软件版本号的字节长度，0表示未获取版本号，255表示不发送车辆软件版本字段
        BYTE sw_version_length=0;
        std::string sw_version;
        //[0..255]，描述自动驾驶系统硬件版本号的字节长度，0表示未获取版本号，255表示不发送自动驾驶系统硬件版本字段
        BYTE hw_version_length=0;
        std::string hw_version;
        //[0..255]，描述自动驾驶系统软件版本号的字节长度，0表示未获取版本号，255表示不发送自动驾驶系统软件版本号字段
        BYTE ad_version_length=0;
        std::string ad_version;
        //枚举类型：[0..2]，0：4G；1：5G；2：其他；255：缺省
        BYTE com_type=0;
        //枚举类型：[0..2]，0：缺省值；1：不支持；2：支持；
       // BYTE pc5_enable_type=0;
        //参考附录D（规范性）精度等级
        BYTE pos_confidence=0;
        //枚举类型：[0..5]，0：缺省值，1：PTP；2：GNSS；3：局域网NTP时钟同步；4：互联网NTP时钟同步；5：其他同步方式
        BYTE time_sync_type=0;
        //见常用枚举类型
        BYTE coordinate_type=0;
        //[0..255]，描述自定义字段长度，0表示没有自定义字段，不发送自定义字段内容
        BYTE user_data_length=0;
        std::string user_data;
        int GetLength()
        {
          return 16+sw_version.length()+hw_version.length()+ad_version.length()+user_data.length();  
        }
    };

    struct INHResData
    {
        // DWORD msg_seq;
        BYTE veh_id[8];
        // BYTE uuid[36];
        //枚举类型:[0..3],0:确认;1:失败;2:消息有误;3:不支持,不可缺省
        BYTE res;
    };

    struct CfgSyncData
    {
        // DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        //设置车辆心跳上报的时间间隔，单位：ms
        DWORD heartbeat_interval=0;
        //枚举类型：[0..3]，车辆实时状态信息触发状态，0：缺省值；1：V1；2：V2；3：V3
        BYTE veh_state_level=0;
        //设置车辆实时状态信息上报的时间间隔，单位：ms
        DWORD veh_state_interval=0;
        //设置车辆运行状态信息上报的时间间隔，单位：ms
        DWORD veh_status_interval=0;
        //车端事件信息上报开关 0：关闭或缺省；1：打开
        BYTE veh_event_update_switch=0;
        //车端感知共享信息上报开关 0：关闭或缺省；1：打开
        BYTE veh_detection_upload_switch=0;
        //日志级别 参见常用字段定义
        BYTE log_level=0;
    };


    struct CfgSyncResData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE do_flag;
    };

    struct CfgReqData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
    };


    struct CfgReqResData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        //设置车辆心跳上报的时间间隔，单位：ms
        DWORD heartbeat_interval=0;
        //枚举类型：[0..3]，车辆实时状态信息触发状态，0：缺省值；1：V1；2：V2；3：V3
        BYTE veh_state_level=0;
        //设置车辆实时状态信息上报的时间间隔，单位：ms
        DWORD veh_state_interval=0;
        //设置车辆运行状态信息上报的时间间隔，单位：ms
        DWORD veh_status_interval=0;
        //车端事件信息上报开关 0：关闭或缺省；1：打开
        BYTE veh_event_update_switch=0;
        //车端感知共享信息上报开关 0：关闭或缺省；1：打开
        BYTE veh_detection_upload_switch=0;
        //日志级别 参见常用字段定义
        BYTE log_level=0;
    };


    struct VehDataReqData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        //数据请求类型 0：无效 1：车辆参数2：地图信息 3：路侧相机临时推流地址 4：证书下载地址
        BYTE data_req_type=0;
        //请求参数长度
        BYTE data_req_param_len=0;
        //请求参数内容
        std::string data_req_param;

        int GetLength()
        {
            return 14+data_req_param_len;
        }
    };

    struct VehDataReqResData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        //数据请求类型 0：无效 1：车辆参数2：地图信息 3：路侧相机临时推流地址 4：证书下载地址
        BYTE data_req_type=0;
        //执行状态 0：失败；1：成功
        BYTE res_flag=0;
        //请求参数长度
        WORD data_res_param_len=0;
        std::string data_res_param;
    };


    struct StatusData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        TIMESTAMP timestamp=0;
        //PC5网络生效标志 枚举类型：[0..1]，0：失效；1：有效
        BYTE network_pc5_valid=0;
        //车辆失效标志 枚举类型：[0..1]，0：失效；1：有效
        BYTE veh_failure=0;
        //智能驾驶系统检测故障标志 枚举类型：[0..1]，0：失效；1：有效，该字段失效则智能驾驶系统系统功能有效标志位全部失效
        BYTE veh_ads_start_failure=0;
        //时间同步有效标志 枚举类型：[0..1]，0：失效；1：有效
        BYTE time_sync_valid=0;
        //智能驾驶系统有效标志 枚举类型：[0..1]，0：失效；1：有效
        BYTE ad_valid=0;
        //车载传感器有效标志 枚举类型：[0..1]，0：失效；1：有效
        BYTE sensor_valid=0;
        //车辆线控系统有效标志 枚举类型：[0..1]，0：失效；1：有效
        BYTE veh_wired_control_valid=0;
        //车载HMI生效标志 枚举类型：[0..1]，0：失效；1：有效
        BYTE veh_hmi_valid=0;
        //调度指令响应能力标志
        BYTE dispatch_enable=0;
        //全局路径控制
        BYTE global_route_enable=0;
        //局部路径控制
        BYTE local_route_enable=0;
        //远程控制
        BYTE remote_control_enable=0;
    };


    struct TrafficEventInfo
    { 
        //事件唯一编号
        BYTE event_id[36];
        //有效标志
        BYTE valid=0;
        //事件类型
        BYTE alert_type=0;
        //事件置信度
        BYTE event_confidence=0;
        //经度
        double longitude=-180.0;
        //纬度
        double latitude=-90.0;
        //高程
        double elevation=-10000;
        //备注信息长度
        BYTE content_len=0;
        //备注
        std::string content;
        //告警真实半径
        float alert_radius=0;
        //告警路径点数量
        BYTE alert_path_point_num=0;
        //告警路径点
        std::vector<WayPointLLE> alert_path_points;

        int GetLength()
        {
            return (55+content_len+alert_path_point_num*12);
        }
    };

    
    
    struct EventData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        TIMESTAMP timestamp;
        //事件数量
        BYTE event_num;
        //事件信息
        std::vector<TrafficEventInfo> traffic_evvent_info;

        int GetLength()
        {
            int length=0;
            length +=21;
            for(auto info:traffic_evvent_info)
            {
                length+=info.GetLength();
            }
            return length;
        }

    };

    struct PtcInfo
    {
        //交通参与者局部编号 [0..255]前后帧同一交通参与者ptcId一致，不可缺省，0标识失效值
        BYTE ptc_id=0;
        //运动状态 0：缺省或失效 1：不可运动目标 2：可运动但速度为0的目标 3：运动中的目标
        BYTE motion_status=0;
        //交通参与者类型 参见附录
        BYTE ptc_type=0;
        //位置
        WayPointLLE position;
        //位置置信度
        BYTE pos_confidence=0;
        //高程置信度
        BYTE ele_confidence=0;
        //速度速度，单位：m/s
        float speed=0;
        //航向角 正北方向顺时针旋转至与车辆当前车头指向方向重合所转过的角度
        float heading=0;
        Size size;
        //颜色信息 定义见常见枚举类
        BYTE color;
        //车牌号
        BYTE plate_No[9];
        //跟踪时长
        DWORD tracking_time;
        //车道编号
        BYTE lane_id;

        int GetLength()
        {
            return 50;
        }
    };
    struct DetectionData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        TIMESTAMP timestamp;
        //交通参与者数量
        BYTE ptc_num=0;
        std::vector<PtcInfo> ptc_infos;

        int GetLength()
        {
            int length=0;
            length +=21;
            for(auto ptc:ptc_infos)
            {
                length+= ptc.GetLength();
            }
            return length;
        }
    };


#pragma pack(1)
    struct FuncReqData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        //按位定义，取值应符合表20中的规定，其中N为功能订阅信息长度，对于每一比特位，0：不订阅该功能；1：订阅该功能
        BYTE func_req[6];
        //枚举类型：[0..2]，0：无效的；1：方向盘转角控制；2：方向盘转向角和转度控制；不可缺省
        BYTE wired_control_mode_hori=0;
        //枚举类型：[0..5]，0：无效的；1：加速踏板控制；2：电机PWM控制；3：扭矩控制；4：速度控制；5：加速度控制；不可缺省
        BYTE wired_control_mode_vert=0;
        //枚举类型：[0..3]，0：无效的；1：制动踏板控制；2：制动压力控制；3：电机PWM控制；
        BYTE wired_control_mode_brake=0;
        //枚举类型：[0..3]，是否允许OTA，0：总是拒绝；1：需要用户确认；2：有条件的允许；3：总是允许；不可缺省
        BYTE ota_permission=0;
        //枚举类型：[0..3]，是否允许视频数据共享，0：总是拒绝；1：需要用户确认；2：有条件的允许；3：总是允许；不可缺省
        BYTE camera_share_permission=0;
        //枚举类型：[0..3]，是否允许感知数据共享，0：总是拒绝；1：需要用户确认；2：有条件的允许；3：总是允许；不可缺省
        BYTE detection_sahre_permission=0;
        //参见附录D（规范性）定位精度等级
        BYTE localizaiton_level=0;
        //枚举类型：[0..3]，车辆实时状态信息触发状态，0：缺省值；1：V1；2：V2；3：V3
        BYTE veh_state_level=0;
        //设置车辆实时状态信息上报的时间间隔，单位：ms
        BYTE veh_state_interval=0;

        BYTE local_map_info_length=0;
        //自定义数据内容，长度为N个字节，其中N为本地地图信息版本名称长度
        std::string local_map_info;
        DWORD GetLength()
        {
            return 28+local_map_info.length();
        }
    };

     struct FuncReqDataRes
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        BYTE func_req[6];
    };


    struct StateV1Data
    {
        // DWORD msg_seq;
        BYTE veh_id[8];
        BYTE msg_id[8];
        // TIMESTAMP timestamp=0;
        //GNSS时间戳
        TIMESTAMP timestamp_gnss=0;
        //GNSS速度
        DWORD velocity_gnss=-100.0;
        //位置
        WayPointLLE position;
        DWORD heading=0;
        //档位枚举类型:[0..50],0:数据失效;1-20:表示手动档车辆前进档对 应档位,1 表示 1 档,2 表示 2 档,以此类推;21-30:表示手动挡车 辆倒档对应档位,21 表示 R1 档,22 表示 R2 档,以此类推;31:D 档 (前进档);32:R 档(倒档);33:P 档(驻车档);34:N 档(空 档);35:S 档(运动模式);36:L 档(低速档);37:H 档;38; HL 档;39-50:预留,不可缺省,0xFF 表示异常
        BYTE tap_pos =0;
        //方向盘转角:[0..20000000],方向盘转角,单位:1e-4°,数据偏移量 1000,表示  -1000.0000°~1000.0000°,左正右负,0xFFFFFFFF 表示缺省
        DWORD steering_angle=0;
        //当前车速:[0..20000],CAN 总线数据中的行驶速度,单位:0.01m/s,0xFFFF 表示缺省
        WORD velocity=0;
        //纵向加速度:[0..20000],车辆行驶纵向加速度,单位:0.01 m/s2,数据偏移量 100,  表示-100.00 m/s2~100.00 m/s2,不可缺省,0xFFFF 表示异常
        WORD acceleration_lon=0;
        //横向加速度:[0..20000],车辆行驶横向加速度,单位:0.01 m/s2,数据偏移量 100,  表示-100.00 m/s2~100.00 m/s2,左正右负，不可缺省,0xFFFF 表示异常
        WORD acceleration_lat=0;
        //垂向加速度:[0..20000],车辆行驶垂向加速度,单位:0.01 m/s2,数据偏移量 100, 表示-100.00 m/s2~100.00 m/s2,沿重力方向向下为正,不可缺省, 0xFFFF 表示异常
        WORD acceleration_ver=0;
        //横摆角速度:[0..20000],横摆角速度,单位:0.01°/s,数据偏移量 100,表示 -100.00 m/s2~100.00 m/s2,顺时针旋转为正,不可缺省,0xFFFF 表示 异常
        WORD yaw_rate=0;
        //油门开度:[0..1000],加速踏板开度,单位:0.1%,0xFFFF 表示缺省
        WORD accel_pos=0;
        //发动机输出转速:[0..20000],发动机输出转速,单位:r/min,0xFFFF 表示缺省
        WORD engine_speed=0;
        //发动机扭矩:[0..500000],发动机输出扭矩,单位:0.01Nm,0xFFFFFFFF 表示缺省
        DWORD engine_torque=0;
        //制动踏板开关:[0..1],制动踏板是否踩下,0:未踩下,1:踩下,0xFF 表示缺省
        BYTE brake_flag=0;
        //制动踏板开度:[0..1000],制动踏板开度,单位:0.1%,0xFFFF 表示缺省
        WORD brake_pos=0;
        //制动主缸压力:[0..50000],主缸制动压力,单位 0.01MPa,,0xFFFF 表示缺省
        WORD brake_pressure=0;
        //油耗:[0..65534],车辆运行百公里油耗,单位 0.01L/100km,0xFFFF 表示缺省
        WORD fule_consumption=0;
        //车辆驾驶模式:枚举类型:[0..9],1:人工接管(人工驾驶);2:单车自控(自动驾 驶);3:云端支持下的人工驾驶;4:云端支持下的自动驾驶;5:非 主驾位置人工驾驶(不启用);6:脱离(非自动驾驶行程自动结束下 的接管);7:远程驾驶(非现场人工驾驶);8:未处于任何驾驶模式; 9:其他未定义状态;0xFF 表示缺省
        BYTE drive_mode=0;
        //目的地位置:车辆当前驾驶任务的终点位置
        WayPointLL dest_position;
        //途经点数量:[0..255],0 表示没有途径点,不发送途经点字段,其他取值都均表示  车辆到目的地终点位置途中所经过的途中目的地点数量
        BYTE pass_points_num=0;
        //途经点:N 个途经点,其中 N 为途径点数量
        std::vector<WayPointLL> pass_points;

        int GetLength()
        {
            int length =0;
            length += 66;
            for(auto point:pass_points)
            {
                length+=point.GetLength();
            }
            return length;
        }
    };
    struct StateV1Data_
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        TIMESTAMP timestamp;
        TIMESTAMP timestamp_gnss;
        WORD velocity_gnss;
        DWORD longitude;
        DWORD latitude;
        DWORD elevation;
        DWORD heading;
        BYTE gnss_status;
    };

    struct StateV2Data
    {
        StateV1Data v1_data;
        //ABS 状态:枚举类型:[0..2],ABS 触发状态,0:ABS 未激活;1:ABS 激活;2:  ABS 功能异常;0xFF 表示缺省
        BYTE abs_flag=0;
        //TCS 状态:枚举类型:[0..2],TCS 触发状态,0:TCS 未激活;1:TCS 激活;2:  TCS 功能异常;0xFF 表示缺省
        BYTE tcs_flag=0;
        //ESP 状态:枚举类型:[0..2],ESP 触发状态,0:ESP 未激活;1:ESP 激活;2:  ESP 功能异常;0xFF 表示缺省
        BYTE esp_flag=0;
        //LKA 状态：枚举类型:[0..3],LKA 触发状态,0:LKA 关闭;1:LKA 待机;2:  LKA 激活;3:LKA 功能异常;0xFF 表示缺省
        BYTE lka_flag=0;
        //ACC 工作模式:枚举类型:[0..5],ACC 工作模式,0:ACC 关闭;1:ACC 待机;2: ACC 速度控制;3:ACC 时距控制;4:超控;5:ACC 功能异常;0xFF 表示缺省
        BYTE acc_mode=0;
        //FCW 状态:枚举类型:[0..2],FCW 触发状态,0:FCW 未激活;1:FCW 激活;2:  FCW 功能异常;0xFF 表示缺省
        BYTE fcw_flag=0;
        //LDW 状态:枚举类型:[0..2],LDW 触发状态,0:LDW 未激活;1:LDW 激活;2:  LDW 功能异常;0xFF 表示缺省
        BYTE ldw_flag=0;
        //AEB 状态:枚举类型:[0..2],AEB 触发状态,0:AEB 未激活;1:AEB 激活;2:  AEB 功能异常;0xFF 表示缺省
        BYTE aeb_flag=0;
        //LCA 状态:枚举类型:[0..1],LCA 触发状态,0:LCA 未激活;1:LCA 激活;0xFF  表示缺省
        BYTE lca_flag=0;
        //DMS 状态:枚举类型:[0..2],DMS 触发状态,  0:DMS 未激活;1:DMS 激活;2:DMS 功能异常;0xFF 表示缺省
        BYTE dms_flag=0;
        //里程:[0..10000000],对应车辆里程表读数,单位:0.1km,  0xFFFFFFFF 表示缺省
        DWORD mileage=0;
        //油量:[0..10000],对应车辆油量表读数,单位:0.1 L,  0xFFFF 表示缺省
        WORD fuel_gauge=0;
        //电池剩余电量:[0..10000],单位:0.01%,  0xFFFF 表示缺省
        WORD soc=0;
        //电池温度:[0..200],单位:摄氏度(°C),  数据偏移量 100,表示-100°C~100°C,0xFF 表示缺省
        BYTE temperature=0;
        //预计续航里程:[0..500000],单位:千米(km),  0xFFFFFFFF 表示缺省
        DWORD endurance=0;
        //车辆故障状态:按位定义,取值应符合表 11 的要求
        WORD veh_fault=0;
        //电机转速:[0..40000],电机输出转速,单位:r/min, 数据偏移量 20000,表示-20000 r/min~20000 r/min,0xFFFF 表示缺 省
        WORD motor_speed=0;
        //电机转矩:[0..1000000],电机输出扭矩,单位:0.01 Nm, 数据偏移量 5000,表示-5000.00 Nm~5000.00 Nm,0xFFFFFFFF 表示缺 省
        DWORD motor_torque=0;
        //运行模式:枚举类型:[1..6],车辆运行模式,1:纯电驱动模式;2:混合驱动 模式;3:行车充电模式;4:能量回收模式;5:停车充电模式;6: 能量混合回充模式;0xFF 表示缺省
        BYTE veh_mode=0;
        //充电状态:枚举类型:[1..5],车辆充电状态, 1:未充电;2:充电准备;3:正在充电;4:充电故障;5:充电结束; 0xFF 表示缺省
        BYTE charge_state=0;
        //动力电池实时电压:[0..10000],动力电池总电压,同时也是车辆的充电电压(当充电枪  状态为正在充电时),单位:0.1V,0xFFFF 表示缺省
        WORD batt_vol=0;
        //动力电池实时电流:[0..40000],动力电池总电流,同时也是车辆充电电流(当充电枪状 态为正充电时),单位:0.01 A, 数据偏移量 200,表示-200.00 A~200.00 A,0xFFFF 表示缺省
        WORD batt_cur=0;
        //喇叭状态:[0..100],单位:%,0xFF 表示缺省
        BYTE horn_state=0;
        //车轮数:[0..100],0 表示未获取或数据异常,不发送轮速及胎压字段
        BYTE wheel_num=0;
        //轮速:N 个车轮轮速,其中 N 为车轮数 [0..40000],表示车轮线速度,单位:0.01 m/s, 数据偏移量 200,表示-200.00 m/s~200.00 m/s,0xFFFF 表示缺省或 未安装轮速传感器,标定车轮的顺序为从车头开始从左到右顺序排列, 例如:前左 1,前左 2,前右 1,前右 2,中左 1,中左 2,中右 1,中 右 2,后左 1,后左 2,后右 1,后右 2,...,以此类推
        




        int GetLength()
        {
            return 152;
        }

    };
    
    struct StateV2Data_
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        TIMESTAMP timestamp;
        TIMESTAMP timestamp_gnss;
        WORD velocity_gnss;
        DWORD longitude;
        DWORD latitude;
        DWORD elevation;
        DWORD heading;
        BYTE gnss_status;
        BYTE tap_pos =0;
        //枚举类型：[0..2]，0：纯发动机驱动；1：纯电驱动；2：油电混合；255：缺省
        BYTE drive_engine_type=0;
        //油门踏板开度 单位：0.1%
        float acc_pedal_pos=0;
        //当前车速 单位：m/s，0xFFFF表示缺省
        float velocity_can=0;
        //发动机输出转速 单位：rpm，0xFFFF表示缺省
        WORD enigne_speed=0; 
        //发动机输出扭矩 单位：Nm，0xFFFFFFFF表示缺省
        float engine_torque=0;
        //电机输出转速 单位：rpm
        int motor_speed=-20000;
        //电机输出扭矩 单位：Nm
        float motor_torque=-500000;
        //电子手刹状态 枚举类型：[1..3]，手刹状态，1：释放；2：驻车；3：故障；0表示缺省
        BYTE parking_brake_flag=0;
        //制动踏板开关 [0..1]，制动踏板是否踩下，0：未踩下，1：踩下，
        BYTE brake_flag=0;
        //制动踏板开度 单位：%
        float brake_pedal_pos=0;
        //主缸制动压力，单位MPa,
        float brake_pressure=0;
        //车轮制动状态，list长度小于3无效；针对四轮车，共有5个元素，第一位标识有效性（0标识无效，1标识有效），剩余4个元素依次标识左前，右前，左后，右后
        BYTE wheel_brake[5]={0,0,0,0,0};
        //方向盘转角，单位： ° 左正右负
        float steering_angle=-1000;
        //方向盘角速度，单位：°/s
        float steering_angle_speed=-100;
        //对应车辆里程表读数，单位：km
        float mileage_total=0;
        //对应启动后行驶里程，单位：km
        float mileage_since_start=0;
        //对应加油或充电后行驶里程，单位：km
        float mileage_since_serviced=0;
        //单位：千米（km）
        float driving_range=0;
        //枚举类型：[0..1]，大灯状态，0：未开灯；1：开灯；
        BYTE head_lights=0;
        //枚举类型：[0..1]，远光灯、近光灯状态，0：近光灯；1：远光灯；
        BYTE near_or_far=0;
        //枚举类型：[0..1]，左转向灯状态，0：近光灯；1：远光灯；，左右转向灯都打开标识双闪
        BYTE left_turn_lights=0;
        //枚举类型：[0..1]，右转向灯状态，0：近光灯；1：远光灯；，左右转向灯都打开标识双闪
        BYTE right_turn_lights=0;
        //枚举类型：[0..1]，喇叭状态，0：未生效；1：生效；
        BYTE horn=0;
        //车辆运行瞬时油耗，单位L/100km
        float consumption_fule=0;
        //车辆运行平均油耗，单位L/100km
        float consumption_ave_fule=0;
        //加油后车辆运行平均油耗，单位1L/100km
        float consumption_ave_fule_since_served=0;
        //油箱剩余油量百分比，单位：%
        float sot=0;
        //动力电池总电压，同时也是车辆的充电电压（当充电枪状态为未充电时），单位：V
        float batt_vol=0;
        //动力电池总电流，同时也是车辆充电电流（当充电枪状态为未充电时），单位：A
        float batt_cur=0;
        //单位：摄氏度（℃
        float batt_max_temper=-100;
        //枚举类型：[1..5]，车辆充电状态，1：未充电；2：充电准备；3：正在充电；4：充电故障；5：充电结束；
        BYTE charge_state=0;
        //动力电池总电压，同时也是车辆的充电电压（当充电枪状态为正在充电时），单位：V
        float charge_voltage=0;
        //动力电池总电流，同时也是车辆充电电流（当充电枪状态为正充电时），单位：A
        float charge_current=0;
        //瞬时电耗 单位：%
        float consumption_power=0;
        //平均电耗 单位：%
        float comsumption_ave=0;
        //启动后平均电耗 单位：%
        float comsumption_ave_since_start=0;
        //电池剩余电量 单位：%
        float soc=0;
        //制动防抱死系统 枚举类型：[0..4]，ABS触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE abs_flag=0;
        //电子刹车分配力系统 枚举类型：[0..4]，EBD触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE ebd_flag=0;
        //车辆动态稳定控制系统 枚举类型：[0..4]，VDC触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE vdc_flag=0;
        //牵引力控制系统或驱动防滑系统 枚举类型：[0..4]，TCS触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE tcs_flag=0;
        //电子制动系统 枚举类型：[0..4]，EBS触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE ebs_flag=0;
        //车身电子稳定系统 枚举类型：[0..4]，ESP触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE esp_flag=0;
        //前向碰撞预警系统 枚举类型：[0..4]，FCW触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE fcw_flag=0;
        //前向碰撞辅助系统 枚举类型：[0..4]，FCA触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE fca_flag=0;
        //前向紧急制动 枚举类型：[0..4]，AEB触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE aeb_flag=0;
        //车道偏离预警 枚举类型：[0..4]，LDW触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE ldw_flag=0;
        //车道保持系统 枚举类型：[0..4]，LKA触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE lka_flag=0;
        //定速巡航驾驶辅助 枚举类型：[0..4]，CC触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE cc_flag=0;
        //自适应巡航驾驶辅助 枚举类型：[0..4]，ACC触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE acc_flag=0;
        //预测性定速巡航驾驶辅助 枚举类型：[0..4]，PCC触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE pcc_flag=0;
        //预测性自适应巡航驾驶辅助 枚举类型：[0..4]，PACC触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE pacc_flag=0;
        //车道保持自适应巡航辅助 枚举类型：[0..4]，LCC触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE lcc_flag=0;
        //变道辅助 枚举类型：[0..4]，LCA触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE lca_flag=0;
        //驾驶员监控系统 枚举类型：[0..4]，DMS触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE dms_flag=0;
        //驾驶员疲劳提醒系统 枚举类型：[0..4]，DAW触发状态，0：无效；1：功能异常；2：用户禁用；3：激活但未生效；4：激活且生效；
        BYTE daw_flag=0;
        //巡航指令设置目标车速 巡航指令设置目标车速，单位：m/s
        float cc_setting_velocity=0;
        //巡航指令实时目标车速 巡航指令实时目标车速，单位：m/s，0xFFFF表示缺省
        float xcc_target_velocity=0;
    };

    struct CtlLocalRouteData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        TIMESTAMP timestamp;
        //控制总开关
        BYTE auto_drive_mode_switch;
        //参考经度
        double ref_longitude;
        //参考纬度
        double ref_latitude;
        //参考高程
        double ref_elevation;
        //参考航向角
        float ref_heading;
        //局部路径路点数量
        BYTE local_routeway_point_num;
        //局部路径路点
        std::vector<TrajectoryPoint> trajectory_points;

        int GetLength()
        {
            int length =38;
            for(auto trajectory:trajectory_points)
            {
                length+=trajectory.GetLength();
            }
            return length;
        }
    };

    struct CtlRemoteDrivingData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        TIMESTAMP timestamp;
        //控制总开关
        BYTE auto_drive_mode_switch;
        //方向盘转角
        float steering_angle;
        //方向盘角速度
        float steering_angular_velocity;
        //油门开度
        float accel_pos;
        //制动踏板开关
        BYTE brake_flag;
        //制动踏板开度
        float brake_pos;
        //档位 枚举类型：[0..50]，0：数据失效；[1..20]：表示手动档车辆前进档对应档位，1表示1档，2表示2档，以此类推；
        //[21..30]：表示手动挡车辆倒档对应档位，21表示R1档，22表示R2档，以此类推；31：D档（前进档）；32：R档（倒档）；
        //33：P档（驻车档）；34：N档（空档）；35：S档（运动模式）；36：L档（低速档）；37：H档；38；HL档；[39..50]：预留，不可缺省，0xFF表示异常
        BYTE tap_pos;
        //车灯状态 车灯控制字段取值应符合表13 的要求 BIT15标识有效位，当BIT15=1时有效
        WORD lights;
        //雨刷器状态 枚举类型，[0..5]，0：失效或缺省；1：低档；2：中档；3：高档；4：自动；5：其他状态；
        BYTE wipers;
        //车门状态 车门控制字段应符合表14 BIT15标识有效位，当BIT15=1时有效
        WORD doors;
        //车窗状态 按位定义 BIT0：0：左前车窗关闭；1：左前车窗开启 BIT1：0：右前车窗关闭；1：右前车窗开启 BIT2：0：左后车窗关闭；1：左后车窗开启
        //BIT3：0：右后车窗关闭；1：右后车窗开启 BIT4：0：天窗关闭；1：天窗开启 BIT5~BIT14：扩展位 BIT15标识有效位，当BIT15=1时有效
        WORD windows;
    };

    struct CtlVelocityData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        TIMESTAMP timestamp;
        //控制总开关
        BYTE auto_drive_mode_switch;
        //当前建议速度
        float advice_velocity;
        //当前建议加速度
        float advice_acceleration;
        //路点数量
        BYTE way_point_num;
        std::vector<WayPointLLEV> way_points;

        int GetLength()
        {
            int length =26;
            for(auto point:way_points)
            {
                length+=point.GetLength();
            }
            return length;
        }
    };

    struct InteractionReqData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        //请求接管类型 0：失效； 1：远程驾驶接管 2：局部路径控制接管 3：纵向车速指令接管
        BYTE control_mode;
         int GetLength()
        {
            return 13;
        }   
    };

    struct InteractionReqResData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        //执行标志 参见常用枚举类
        BYTE do_flag;
        //错误码 0：缺省或失效 1：当前车辆不支持接管 2：当前车辆不具备权限 3：预留
        BYTE error_code;
        BYTE content_len;
        std::string content;
        int GetLength()
        {
            return 51+content_len;
        }
    };


    struct InteractionSyncData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        //请求接管类型 0：失效； 1：远程驾驶接管 2：局部路径控制接管 3：纵向车速指令接管
        BYTE control_mode;
         int GetLength()
        {
            return 49;
        }   
    };

    struct InteractionSyncResData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        //执行标志 参见常用枚举类
        BYTE do_flag;
        //错误码 0：缺省或失效 1：当前车辆不支持接管 2：当前车辆不具备权限 3：预留
        BYTE error_code;
        BYTE content_len;
        std::string content;
        int GetLength()
        {
            return 51+content_len;
        }
    };

    struct RemoteDispatchData
    { 
        //调度类型 0：失效 1：立即去往终点 2：按时间计划去往终点
        BYTE dispatch_type =0;
        WayPointLLE target_position;
        TIMESTAMP start_time=0;
        TIMESTAMP arrive_time=0;
        int GetLength()
        {
            return 29;
            
        }
    };

//远程调度指令响应
    struct RemoteDispatchResData
    {
        //执行标志 参见常用枚举类
        BYTE do_flag=0;
        //错误码 0：缺省或失效；1：其他原因；2：用户拒绝响应；3：当前车辆不具备响应能力；4：车辆驾驶安全状态不满足响应条件；5：数据异常；6：任务冲突；
        BYTE error_code=0;
        TIMESTAMP expected_arrive_time=0;
        int GetLength()
        {
            return 10;
        }
    };


    struct RoadSeqData
    {
        BYTE road_name_length=0;
        std::string road_name;
        int GetLength()
        {
            return road_name_length+1;
        }
    };

    struct GlobalRoutingData
    {
        //地图信息长度
        BYTE map_info_len=0;
        //地图信息
        std::string map_info;
        //道路序列数量
        BYTE road_num;
        std::vector<RoadSeqData> road_seq;
        int GetLength()
        {
            int length =0;
            length += map_info_len+2;
            for(auto road:road_seq)
            {
                length += road.GetLength();
            }
            return length;
        }
    };

    struct GlobalRoutingResData
    {
        BYTE do_flag=0;
        BYTE error_code=0;
        int GetLength()
        {
            return 2;
        }
    };
//车速诱导建议
    struct OSAData
    {
        //路点数量
        WORD way_points_num=0;
        //路点
        std::vector<WayPointLLE> way_points;
        int GetLength()
        {
            int length =2;
            for(auto point:way_points)
            {
                length += point.GetLength();
            }
            return length;
        }
    };

    struct OSAResData
    {
        BYTE do_flag=0;
        BYTE error_code=0;
        int GetLength()
        {
            return 2;
        }
    };

//紧急接管
    struct EmergencyTakeoverData
    {
        //接管类型 0：失效 1：原地停车 2：继续行驶（紧急程度字段无效） 3：靠边停车（紧急程度字段无效）
        BYTE take_charge_option=0;
        //紧急程度 0：失效； 1：测试——减速度<2m/s2 2：普通——减速度<5m/s2 3：紧急——减速度<10m/s2 4：危险——制动踏板深度100%
        BYTE emergency_level=0;
        int GetLength()
        {
            return 2;
        }

    };

    struct EmergencyTakeoverResData
    {
        BYTE do_flag=0;
        BYTE error_code=0;
        int GetLength()
        {
            return 2;
        }
    };

    //云端下发车辆驾驶建议指令
    struct CmdDriveData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        //请求接管类型 0：失效； 1：紧急接管指令 2：调度指令 3：全局路径规划指令 4：全局车速诱导指令
        BYTE cmd_type =0;
        BYTE cmd_data_len=0;
        RemoteDispatchData dispatch_data;
        GlobalRoutingData global_routing_data;
        OSAData osa_data;
        EmergencyTakeoverData eto_data;
        int GetLength()
        {
            int length =50;
            switch(cmd_type)
            {
                case 0:
                break;
                case 1:
                length += eto_data.GetLength();
                break;
                case 2 :
                length += dispatch_data.GetLength();
                break;
                case 3:
                length += global_routing_data.GetLength();
                break;
                case 4:
                length += osa_data.GetLength();
                break;
            }
        }
    };

    struct CmdDriveResData
    {
        DWORD msg_seq;
        BYTE veh_id[8];
        BYTE uuid[36];
        //请求接管类型 0：失效； 1：紧急接管指令 2：调度指令 3：全局路径规划指令 4：全局车速诱导指令
        BYTE cmd_type =0;
        //指令响应数据长度
        BYTE cmd_res_data_len=0;
        BYTE content_num;
        std::string content;
        RemoteDispatchResData dispatch_res;
        OSAResData osa_res;
        GlobalRoutingResData global_routing_res;
        EmergencyTakeoverResData eto_res;
        int GetLength()
        {
             int length =50;
            switch(cmd_type)
            {
                case 0:
                break;
                case 1:
                length += eto_res.GetLength();
                break;
                case 2 :
                length += dispatch_res.GetLength();
                break;
                case 3:
                length += global_routing_res.GetLength();
                break;
                case 4:
                length += osa_res.GetLength();
                break;
            }
            return length;
        }
    };


    enum {CLOUD2VEH_ADVICE_GLOSA=0,CLOUD2VEH_ADVICE_NTLAR,CLOUD2VEH_ADVICE_LANESPDLMT,CLOUD2VEH_ADVICE_RAMP_INTENT_CHANGE,
    CLOUD2VEH_ADVICE_FCW,CLOUD2VEH_ADVICE_AVW,CLOUD2VEH_ADVICE_EVW,CLOUD2VEH_ADVICE_RAMP_ASSIST,CLOUD2VEH_ADVICE_COM_RSI,
    CLOUD2VEH_ADVICE_GUIDANCE};


    static std::unordered_map<std::string,int> FunReqMap = {
    {"CLOUD2VEH_ADVICE_GLOSA",CLOUD2VEH_ADVICE_GLOSA},
    {"CLOUD2VEH_ADVICE_NTLAR",CLOUD2VEH_ADVICE_NTLAR},
    {"CLOUD2VEH_ADVICE_LANESPDLMT",CLOUD2VEH_ADVICE_LANESPDLMT},
    {"CLOUD2VEH_ADVICE_RAMP_INTENT_CHANGE",CLOUD2VEH_ADVICE_RAMP_INTENT_CHANGE},
    {"CLOUD2VEH_ADVICE_FCW",CLOUD2VEH_ADVICE_FCW},
    {"CLOUD2VEH_ADVICE_AVW",CLOUD2VEH_ADVICE_AVW},
    {"CLOUD2VEH_ADVICE_EVW",CLOUD2VEH_ADVICE_EVW},
    {"CLOUD2VEH_ADVICE_RAMP_ASSIST",CLOUD2VEH_ADVICE_RAMP_ASSIST},
    {"CLOUD2VEH_ADVICE_COM_RSI",CLOUD2VEH_ADVICE_COM_RSI},
    {"CLOUD2VEH_ADVICE_GUIDANCE",CLOUD2VEH_ADVICE_GUIDANCE},
    };

    static std::unordered_map<int,std::string> FunReqReverseMap = {
    {CLOUD2VEH_ADVICE_GLOSA,"CLOUD2VEH_ADVICE_GLOSA"},
    {CLOUD2VEH_ADVICE_NTLAR,"CLOUD2VEH_ADVICE_NTLAR"},
    {CLOUD2VEH_ADVICE_LANESPDLMT,"CLOUD2VEH_ADVICE_LANESPDLMT"},
    {CLOUD2VEH_ADVICE_RAMP_INTENT_CHANGE,"CLOUD2VEH_ADVICE_RAMP_INTENT_CHANGE"},
    {CLOUD2VEH_ADVICE_FCW,"CLOUD2VEH_ADVICE_FCW"},
    {CLOUD2VEH_ADVICE_AVW,"CLOUD2VEH_ADVICE_AVW"},
    {CLOUD2VEH_ADVICE_EVW,"CLOUD2VEH_ADVICE_EVW"},
    {CLOUD2VEH_ADVICE_RAMP_ASSIST,"CLOUD2VEH_ADVICE_RAMP_ASSIST"},
    {CLOUD2VEH_ADVICE_COM_RSI,"CLOUD2VEH_ADVICE_COM_RSI"},
    {CLOUD2VEH_ADVICE_GUIDANCE,"CLOUD2VEH_ADVICE_GUIDANCE"}

    };

// rcu struct
    //摄像头状态
    struct CameraStatus
    {
        BYTE id;
        // 摄像头ID
        BYTE cam_id[11];
        // 摄像头状态 枚举类型：[1..3] 1:OFF 关 2:OK 正常 3:ERROR 出错
        BYTE cam_status;
        int getLength()
        {
            return 13;
        }
    };

    //雷达状态
    struct RadarStatus
    {
        BYTE id;
        // 雷达ID
        BYTE radar_id[11];
        // 雷达状态 枚举类型：[1..3] 1:OFF 关 2:OK 正常 3:ERROR 出错
        BYTE radar_status;
        int getLength()
        {
            return 13;
        }
    };

    //激光雷达状态
    struct LidarStatus
    {
        BYTE id;
        // 传感器ID
        BYTE lidar_id[11];
        // 传感器状态 枚举类型：[1..3] 1:OFF 关 2:OK 正常 3:ERROR 出错
        BYTE lidar_status;
        int getLength()
        {
            return 13;
        }
    };

    struct RCUStatusData
    {
       // DWORD msg_seq;
        BYTE channel_id;
        BYTE rcu_id[8];
        WORD status;
        BYTE cam_num;
        std::vector<CameraStatus> cam_status_list;
        BYTE radar_num;
        std::vector<RadarStatus> radar_status_list;
        BYTE lidar_num;
        std::vector<LidarStatus> lidar_status_list;
        int getLength()
        {
            int length = 14;
            for(auto cam:cam_status_list)
            {
                length += cam.getLength();
            }
            for(auto radar:radar_status_list)
            {
                length += radar.getLength();
            }
            for(auto lidar:lidar_status_list)
            {
                length += lidar.getLength();
            }
        }
    };

} // namespace cvis_bcl
